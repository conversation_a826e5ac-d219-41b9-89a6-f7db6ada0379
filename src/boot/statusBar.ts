import { boot } from 'quasar/wrappers'
import { isApp } from '../config'

export let safeTop = ''

export function setSafeTop(value: string) {
  safeTop = value
}

export function updateSafeAreaTop() {
  const el = document.querySelector('.q-body--capacitor')  as HTMLElement
  if (el) {
    el.style.setProperty('--safe-area-top', safeTop)
  }
}


export default boot(async () => {
  if (!isApp) {
    return
  }

  try {
    // 只有在 App 环境下再动态导入 Capacitor 模块
    const { initStatusBar } = await import('./capacitorConfig')
    await initStatusBar()

    // 添加安全区域类
    document.body.classList.add('q-body--capacitor')
  } catch (err) {
    // 输出错误信息，便于调试
    // eslint-disable-next-line no-console
    console.error('[StatusBar] 初始化失败:', err)
  }
})
